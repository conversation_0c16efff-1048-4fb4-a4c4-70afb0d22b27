/**
 * Utility functions for preventing caching of sensitive content
 * Addresses CWE-524: Storable and Cacheable Content vulnerability
 */

/**
 * Get cache prevention headers for API responses
 */
export const getCachePreventionHeaders = () => ({
  "Cache-Control": "no-cache, no-store, must-revalidate, private",
  Pragma: "no-cache",
  Expires: "0",
  "X-Content-Type-Options": "nosniff",
  Vary: "Authorization, Accept-Encoding",
});

/**
 * Get fetch options with cache prevention
 */
export const getCachePreventionFetchOptions = (
  options: RequestInit = {}
): RequestInit => ({
  ...options,
  cache: "no-store",
  credentials: "same-origin",
  headers: {
    ...getCachePreventionHeaders(),
    ...options.headers,
  },
});

/**
 * Check if content contains sensitive information that should not be cached
 */
export const isSensitiveContent = (data: any): boolean => {
  if (!data || typeof data !== "object") return false;

  const sensitiveFields = [
    "email",
    "phone",
    "ssn",
    "passport",
    "applicationId",
    "studentId",
    "personalInfo",
    "address",
    "dateOfBirth",
    "financialInfo",
    "documents",
  ];

  const dataString = JSON.stringify(data).toLowerCase();
  return sensitiveFields.some((field) =>
    dataString.includes(field.toLowerCase())
  );
};

/**
 * Add meta tags to prevent page caching in browser history
 */
export const addNoCacheMetaTags = () => {
  if (typeof window === "undefined") return;

  const metaTags = [
    { name: "cache-control", content: "no-cache, no-store, must-revalidate" },
    { name: "pragma", content: "no-cache" },
    { name: "expires", content: "0" },
  ];

  metaTags.forEach(({ name, content }) => {
    let meta = document.querySelector(`meta[name="${name}"]`);
    if (!meta) {
      meta = document.createElement("meta");
      meta.setAttribute("name", name);
      document.head.appendChild(meta);
    }
    meta.setAttribute("content", content);
  });
};

/**
 * Clear sensitive data from browser storage
 */
export const clearSensitiveStorage = () => {
  if (typeof window === "undefined") return;

  try {
    // Clear session storage
    sessionStorage.clear();

    // Clear specific localStorage items that might contain sensitive data
    const sensitiveKeys = [
      "email",
      "applicationId",
      "studentDetails",
      "formData",
      "accessToken",
    ];

    sensitiveKeys.forEach((key) => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.warn("Failed to clear sensitive storage:", error);
  }
};
